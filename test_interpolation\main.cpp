
/**
 * @file main.cpp
 * @brief ADMotion直线插补测试Qt控制台程序
 * <AUTHOR> Team
 * @date 2024-12-19
 *
 * Qt控制台版本的ADMotion直线插补测试程序
 * 支持Qt 5.12.12，使用C++17标准
 * 包含完整的测试流程
 */

#include <QCoreApplication>
#include <QDir>
#include <QFile>
#include <QTextCodec>
#include <QDebug>
#include <QTimer>
#include <QDateTime>
#include "../admc_pci.h"

// 常量定义
const short AXIS_STATUS_IN_POSITION_BIT = 4;   // 到位状态位（第5位，从0开始计数）
const short AXIS_STATUS_MOVING_BIT = 5;        // 运动状态位（第6位，从0开始计数）
const short SUCCESS_CODE = 0;                  // 成功返回码

// 全局变量
TADMotionConn* g_handle = nullptr;
QTimer* g_timer = nullptr;
QCoreApplication* g_app = nullptr;
int g_currentStep = 0;
int g_axisCheckCount = 0;
QDateTime g_stepStartTime;

// 线段定义结构
struct LineSegment {
    int32_t x;
    int32_t y;
    QString name;

    LineSegment(int32_t x_val, int32_t y_val, const QString &name_val)
        : x(x_val), y(y_val), name(name_val) {}
};

QList<LineSegment> g_lineSegments;
int g_currentSegmentIndex = 0;

// 测试参数
const QString g_ip = "***********";
const int g_port = 6666;
const double g_maxVel = 3000.0;
const double g_maxAcc = 20.0;
const double g_lineVel = 1000.0;
const double g_lineAcc = 10.0;

// 函数声明
void logMessage(const QString &message);
void logError(const QString &error);
bool connectToBoard();
void disconnectFromBoard();
bool enableAxes();
bool setCoordinateParameters();
bool executeLineSegment(const LineSegment &segment);
bool checkAxesStatus();
void processNextStep();
void finishTest(bool success);

void logMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    qDebug() << QString("[%1] %2").arg(timestamp, message);
}

void logError(const QString &error)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    qCritical() << QString("[%1] 错误: %2").arg(timestamp, error);
}

bool connectToBoard()
{
    logMessage("1. 创建运动控制板卡句柄...");
    g_handle = API_CreateBoard();
    if (!g_handle) {
        logError("创建板卡句柄失败！");
        return false;
    }
    logMessage(QString("板卡句柄创建成功: %1").arg(reinterpret_cast<quintptr>(g_handle), 0, 16));

    logMessage("2. 连接运动控制板卡...");
    logMessage(QString("连接参数: IP=%1, 端口=%2").arg(g_ip).arg(g_port));

    short ret = API_OpenBoard(g_handle, g_ip.toLocal8Bit().data(), g_port);
    if (ret != SUCCESS_CODE) {
        logError(QString("连接板卡失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage("板卡连接成功！");
    return true;
}

void disconnectFromBoard()
{
    if (g_handle) {
        API_CloseBoard(g_handle);
        API_DeleteBoard(g_handle);
        g_handle = nullptr;
        logMessage("板卡连接已断开");
    }
}

bool enableAxes()
{
    logMessage("3. 使能轴...");

    // 使能轴0和轴1
    for (int i = 0; i < 2; i++) {
        short ret = API_AxisOn(g_handle, i);
        if (ret != SUCCESS_CODE) {
            logError(QString("使能轴%1失败！错误码: %2").arg(i).arg(ret));
            return false;
        }
        logMessage(QString("轴%1使能成功").arg(i));
    }

    logMessage("所有轴使能完成");
    return true;
}

bool setCoordinateParameters()
{
    logMessage("4. 设置坐标系参数...");
    short crd = 0;

    short ret = API_SetCrdPrm(g_handle, crd, g_maxVel, g_maxAcc);
    if (ret != SUCCESS_CODE) {
        logError(QString("设置坐标系参数失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage(QString("坐标系参数设置成功！坐标系: %1, 最大速度: %2, 最大加速度: %3")
               .arg(crd).arg(g_maxVel).arg(g_maxAcc));
    return true;
}





bool checkAxesStatus()
{
    short axis0Status = 0, axis1Status = 0;
    short axis0 = 0, axis1 = 1; // 坐标系0对应轴0,1

    short ret0 = API_GetAxisStatus(g_handle, axis0, axis0Status);
    short ret1 = API_GetAxisStatus(g_handle, axis1, axis1Status);

    if (ret0 != SUCCESS_CODE || ret1 != SUCCESS_CODE) {
        logError(QString("获取轴状态失败！轴%1返回码: %2, 轴%3返回码: %4")
                 .arg(axis0).arg(ret0).arg(axis1).arg(ret1));
        return false;
    }

    // 检查两个轴是否都到位且不在运动中
    // 第5位（bit4）是到位信号：1=到位，0=未到位
    // 第6位（bit5）是运动信号：1=运动中，0=停止
    // 轴停止条件：到位=1 且 运动=0
    bool axis0Stopped = ((axis0Status>>4)&0x01) && (((axis0Status>>5)& 1));
    bool axis1Stopped = ((axis1Status>>4)&0x01) && (((axis1Status>>5)& 1));

    g_axisCheckCount++;
    if (g_axisCheckCount % 10 == 0) {  // 每1秒打印一次状态
        qint64 elapsedMs = g_stepStartTime.msecsTo(QDateTime::currentDateTime());

        // 解析轴0状态
        bool axis0InPosition = (axis0Status>>4)&0x01;
        bool axis0Moving = (axis0Status>>5)&0x01;

        // 解析轴1状态
        bool axis1InPosition = (axis1Status>>4)&0x01;
        bool axis1Moving = (axis1Status>>5)&0x01;

        logMessage(QString("检查次数: %1, 轴%2[到位:%3,运动:%4], 轴%5[到位:%6,运动:%7] (已用时: %8ms)")
                   .arg(g_axisCheckCount)
                   .arg(axis0).arg(axis0InPosition ? "true" : "false").arg(axis0Moving ? "true" : "false")
                   .arg(axis1).arg(axis1InPosition ? "true" : "false").arg(axis1Moving ? "true" : "false")
                   .arg(elapsedMs));
    }

    if (axis0Stopped && axis1Stopped) {
        qint64 elapsedMs = g_stepStartTime.msecsTo(QDateTime::currentDateTime());
        logMessage(QString("坐标系 0 的轴已停止！用时: %1ms").arg(elapsedMs));
        return true;
    }

    return false;
}





bool LineSegment(double x,double y)
{
    logMessage(QString("目标坐标: (%1, %2), 速度: %3, 加速度: %4")
               .arg(x).arg(y).arg(100).arg(10));

    // 发送直线插补指令
    short ret = API_Ln(g_handle, 0, x, y, 100, 10);
    if (ret != SUCCESS_CODE) {
        logError(QString("发送直线插补指令失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage("直线插补指令发送成功");

    // 启动坐标系插补
    ret = API_CrdStart(g_handle, 0);
    if (ret != SUCCESS_CODE) {
        logError(QString("启动坐标系插补失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage("坐标系插补启动成功");

    return true;
}

int sertLine(double x,double y)
{
    bool set =  setCoordinateParameters();
    if(set!=true)
    {
        return false;
    }
    bool LineSet =  LineSegment(x,y);
    if(LineSet!=true)
    {
        return false;
    }
    return true;
}

bool DcheckAxesStatus()
{
    short axis0Status = 0, axis1Status = 0;
    short axis0 = 0, axis1 = 1; // 坐标系0对应轴0,1

    short ret0 = API_GetAxisStatus(g_handle, axis0, axis0Status);
    short ret1 = API_GetAxisStatus(g_handle, axis1, axis1Status);

    if (ret0 != SUCCESS_CODE || ret1 != SUCCESS_CODE) {
        logError(QString("获取轴状态失败！轴%1返回码: %2, 轴%3返回码: %4")
                 .arg(axis0).arg(ret0).arg(axis1).arg(ret1));
        return false;
    }

    // 检查两个轴是否都到位且不在运动中
    // 第5位（bit4）是到位信号：1=到位，0=未到位
    // 第6位（bit5）是运动信号：1=运动中，0=停止
    // 轴停止条件：到位=1 且 运动=0
    bool axis0Stopped = ((axis0Status>>4)&0x01) && (((axis0Status>>5)& 1));
    bool axis1Stopped = ((axis1Status>>4)&0x01) && (((axis1Status>>5)& 1));

    if (axis0Stopped && axis1Stopped) {
        return true;
    }

    return false;
}


bool checkStatus()
{
    bool runEnd = true;
    while(runEnd)
    {
       runEnd = !(DcheckAxesStatus());
    }
    return true;
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    g_app = &app;

    // 设置应用程序信息
    app.setApplicationName("ADMotion直线插补测试");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("ADMotion Team");
    app.setOrganizationDomain("admotion.com");

    // 设置中文编码支持（Qt5需要）
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
#endif

    qDebug() << "=== ADMotion 直线插补测试程序 (Qt版) ===";
    qDebug() << "测试内容: 四段直线插补（矩形路径）";
    qDebug() << "坐标系: 0, 轴: 0和1";
    qDebug() << "Qt版本:" << QT_VERSION_STR;
    qDebug() << "=======================================";

    // 检查DLL文件是否存在
    QString dllPath = QDir::currentPath() + "/ADMotion.dll";
    if (!QFile::exists(dllPath)) {
        qWarning() << "警告：未找到ADMotion.dll文件！";
        qWarning() << "请确保DLL文件位于：" << dllPath;
        qWarning() << "程序可能无法正常运行。";
    }
    connectToBoard();
    enableAxes();
    bool lineRun = false;
    double x1 = 10000;double y1 = 10000;
    double x2 = 10000;double y2 = 10000;
    double x3 = 10000;double y3 = 10000;
    double x4 = 10000;double y4 = 10000;
    //checkStatus();
    lineRun = sertLine(x1,y1);
    qDebug() << "第一段返回值:" << lineRun;
    checkStatus();
    lineRun = sertLine(x2,y2);
   qDebug() << "第2段返回值:" << lineRun;
    checkStatus();
    lineRun = sertLine(x3,y3);
    qDebug() << "第3段返回值:" << lineRun;
    checkStatus();
    lineRun = sertLine(x4,y4);
    qDebug() << "第4段返回值:" << lineRun;
    disconnectFromBoard();
    return app.exec();
}
